import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:smsautoforwardapp/model/forward_all_url_model.dart';
import 'package:smsautoforwardapp/model/subscription_model.dart';
import 'forward_all_email_model.dart';

class UserModel {
  String? name;
  String? email;
  String? uid;
  String? stripeCustomerID;
  int? noOfForwardsused;
  int? noOfForwardsPerMonth;
  bool? isPremium;
  int? deviceCount; // Track number of active devices
  SubsriptionModel? currentSubscription;
  ForwaredAllURLModelClass? forwardAllUrl;
  ForwaredAllEmailModelClass? forwardAllEmail;
  Timestamp? createdAt;
  DateTime? subscriptionEndsAt; // New field

  UserModel({
    this.name,
    this.email,
    this.isPremium,
    this.deviceCount,
    this.stripeCustomerID,
    this.forwardAllEmail,
    this.currentSubscription,
    this.forwardAllUrl,
    this.noOfForwardsused,
    this.noOfForwardsPerMonth,
    this.uid,
    this.createdAt,
    this.subscriptionEndsAt,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      name: json['name'],
      stripeCustomerID: json['stripeCustomerID'],
      isPremium: json['isPremium'],
      deviceCount: json['deviceCount'] ?? 0, // Default to 0 for old users
      noOfForwardsused: json['noOfForwardsused'],
      noOfForwardsPerMonth: json['noOfForwardsPerMonth'],
      currentSubscription: json['currentSubscription'] != null
          ? SubsriptionModel.fromJson(json['currentSubscription'])
          : null,
      email: json['email'],
      uid: json['uid'],
      forwardAllEmail: json['forwardAllEmail'] != null
          ? ForwaredAllEmailModelClass.fromJson(json['forwardAllEmail'])
          : null,
      forwardAllUrl: json['forwardAllUrl'] != null
          ? ForwaredAllURLModelClass.fromJson(json['forwardAllUrl'])
          : null,
      createdAt: json['createdAt'],
      subscriptionEndsAt: json['subscriptionEndsAt'] != null
          ? (json['subscriptionEndsAt'] as Timestamp).toDate()
          : null,
    );
  }

  Map<String, dynamic> toJson() => {
        "name": name,
        "email": email,
        "stripeCustomerID": stripeCustomerID,
        "isPremium": isPremium,
        "deviceCount": deviceCount,
        "noOfForwardsused": noOfForwardsused,
        "noOfForwardsPerMonth": noOfForwardsPerMonth,
        "currentSubscription": currentSubscription?.toJson(),
        "forwardAllEmail": forwardAllEmail?.toJson(),
        "forwardAllUrl": forwardAllUrl?.toJson(),
        "uid": uid,
        "createdAt": createdAt,
        "subscriptionEndsAt": subscriptionEndsAt != null
            ? Timestamp.fromDate(subscriptionEndsAt!)
            : null,
      };

  static UserModel fromSnap(DocumentSnapshot snap) {
    var snapshot = snap.data() as Map<String, dynamic>;
    return UserModel(
      email: snapshot['email'],
      stripeCustomerID: snapshot['stripeCustomerID'],
      uid: snapshot['uid'],
      isPremium: snapshot['isPremium'],
      deviceCount: snapshot['deviceCount'] ?? 0, // Default to 0 for old users
      currentSubscription: snapshot['currentSubscription'] != null
          ? SubsriptionModel.fromJson(snapshot['currentSubscription'])
          : null,
      noOfForwardsused: snapshot['noOfForwardsused'],
      noOfForwardsPerMonth: snapshot['noOfForwardsPerMonth'],
      forwardAllEmail: snapshot['forwardAllEmail'] != null
          ? ForwaredAllEmailModelClass.fromJson(snapshot['forwardAllEmail'])
          : null,
      forwardAllUrl: snapshot['forwardAllUrl'] != null
          ? ForwaredAllURLModelClass.fromJson(snapshot['forwardAllUrl'])
          : null,
      name: snapshot['name'],
      createdAt: snapshot['createdAt'],
      subscriptionEndsAt: snapshot['subscriptionEndsAt'] != null
          ? (snapshot['subscriptionEndsAt'] as Timestamp).toDate()
          : null,
    );
  }
}
