import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:smsautoforwardapp/model/user.dart';
import 'package:smsautoforwardapp/model/subscription_model.dart';
import 'package:smsautoforwardapp/model/forward_all_email_model.dart';
import 'package:smsautoforwardapp/model/forward_all_url_model.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();

  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: TestUserCreation(),
    );
  }
}

class TestUserCreation extends StatefulWidget {
  @override
  _TestUserCreationState createState() => _TestUserCreationState();
}

class _TestUserCreationState extends State<TestUserCreation> {
  String _status = 'Ready to test';

  Future<void> testUserCreation() async {
    setState(() {
      _status = 'Testing user creation...';
    });

    try {
      // Create a test user model
      final testUser = UserModel(
          name: 'Test User',
          email: '<EMAIL>',
          stripeCustomerID: '',
          noOfForwardsused: 0,
          noOfForwardsPerMonth: 50,
          createdAt: Timestamp.fromMillisecondsSinceEpoch(
              DateTime.now().millisecondsSinceEpoch),
          uid: 'test-uid-${DateTime.now().millisecondsSinceEpoch}',
          isPremium: false,
          isAlreadyLoggedIn: false,
          currentSubscription: SubsriptionModel(
              subscriptionItem: '',
              subscriptionID: '',
              subscriptionPlan: '',
              planDescription: ''),
          forwardAllEmail:
              ForwaredAllEmailModelClass(isActive: false, recipients: ['']),
          forwardAllUrl: ForwaredAllURLModelClass(
              isActive: false, method: '', url: '', jsonBody: ''));

      print('Test: Created UserModel: ${testUser.email}');

      // Test toJson conversion
      final userJson = testUser.toJson();
      print('Test: UserModel JSON: $userJson');

      // Test Firestore connection
      final firestore = FirebaseFirestore.instance;
      print('Test: Firestore instance created');

      // Try to create a test document
      await firestore.collection('test_users').doc(testUser.uid).set(userJson);

      print('Test: Document created successfully');

      // Try to read it back
      final doc =
          await firestore.collection('test_users').doc(testUser.uid).get();

      if (doc.exists) {
        print('Test: Document read successfully: ${doc.data()}');
        setState(() {
          _status = 'SUCCESS: User creation works!';
        });
      } else {
        setState(() {
          _status = 'ERROR: Document was not created';
        });
      }

      // Clean up test document
      await firestore.collection('test_users').doc(testUser.uid).delete();
      print('Test: Cleanup completed');
    } catch (e) {
      print('Test: Error occurred: $e');
      setState(() {
        _status = 'ERROR: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Test User Creation'),
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Status: $_status',
              style: TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 20),
            ElevatedButton(
              onPressed: testUserCreation,
              child: Text('Test User Creation'),
            ),
          ],
        ),
      ),
    );
  }
}
